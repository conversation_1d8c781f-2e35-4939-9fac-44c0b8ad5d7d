# Setup Jenkins CI/CD

## 1. <PERSON><PERSON><PERSON> đặt Jenkins

### Ubuntu/Debian:
```bash
# Update system
sudo apt update

# Install Java
sudo apt install openjdk-11-jdk -y

# Add Jenkins repository
curl -fsSL https://pkg.jenkins.io/debian/jenkins.io.key | sudo tee /usr/share/keyrings/jenkins-keyring.asc > /dev/null
echo deb [signed-by=/usr/share/keyrings/jenkins-keyring.asc] https://pkg.jenkins.io/debian binary/ | sudo tee /etc/apt/sources.list.d/jenkins.list > /dev/null

# Install Jenkins
sudo apt update
sudo apt install jenkins -y

# Start Jenkins
sudo systemctl start jenkins
sudo systemctl enable jenkins

# Install Docker
sudo apt install docker.io -y
sudo usermod -aG docker jenkins
sudo systemctl restart jenkins
```

## 2. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>

### Truy cập Jenkins:
- URL: http://your-server:8080
- Password: `sudo cat /var/lib/jenkins/secrets/initialAdminPassword`

### Cài đặt plugins cần thiết:
- **Git Plugin** (thường đã có sẵn)
- **Docker Pipeline Plugin**
- **GitHub Integration Plugin**
- **Pipeline Plugin** (thường đã có sẵn)

## 3. Tạo Jenkins Jobs

### Cho Backend (dh-index-be):
1. **New Item** → **Pipeline** → Tên: `dh-index-backend`
2. **Pipeline Definition**: Pipeline script from SCM
3. **SCM**: Git
4. **Repository URL**: https://github.com/daihiep-index/dh-index-be.git
5. **Script Path**: Jenkinsfile
6. **Branch**: main

### Cho Frontend (dh-index-fe):
1. **New Item** → **Pipeline** → Tên: `dh-index-frontend`
2. **Pipeline Definition**: Pipeline script from SCM
3. **SCM**: Git
4. **Repository URL**: https://github.com/daihiep-index/dh-index-fe.git
5. **Script Path**: Jenkinsfile
6. **Branch**: main

## 4. Cấu hình GitHub Webhooks

### Trong GitHub Repository:
1. **Settings** → **Webhooks** → **Add webhook**
2. **Payload URL**: http://your-jenkins-server:8080/github-webhook/
3. **Content type**: application/json
4. **Events**: Just the push event
5. **Active**: ✅

## 5. Environment Variables (Optional)

### Trong Jenkins Global Configuration:
- `DOCKER_REGISTRY`: your-registry.com
- `DEPLOY_SERVER`: your-production-server.com
- `NOTIFICATION_EMAIL`: <EMAIL>

## 6. Security Best Practices

### Jenkins Security:
```bash
# Tạo user riêng cho Jenkins
sudo useradd -m -s /bin/bash jenkins-deploy
sudo usermod -aG docker jenkins-deploy

# Cấu hình SSH keys cho deployment
sudo -u jenkins-deploy ssh-keygen -t rsa -b 4096
```

### Production Server:
```bash
# Tạo thư mục deployment
sudo mkdir -p /opt/dh-index-backend
sudo mkdir -p /opt/dh-index-frontend
sudo chown jenkins-deploy:jenkins-deploy /opt/dh-index-*
```

## 7. Monitoring & Notifications

### Slack Integration (Optional):
1. Install **Slack Notification Plugin**
2. Configure Slack workspace
3. Add notifications to Jenkinsfile:
```groovy
post {
    success {
        slackSend color: 'good', message: "✅ Deployment successful: ${env.JOB_NAME} - ${env.BUILD_NUMBER}"
    }
    failure {
        slackSend color: 'danger', message: "❌ Deployment failed: ${env.JOB_NAME} - ${env.BUILD_NUMBER}"
    }
}
```
