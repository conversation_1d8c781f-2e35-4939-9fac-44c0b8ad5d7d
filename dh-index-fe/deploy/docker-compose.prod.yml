version: '3.8'

services:
  dh-index-frontend:
    image: dh-index-frontend:latest
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=production
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-https://index-be.daihiep.click/api}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
