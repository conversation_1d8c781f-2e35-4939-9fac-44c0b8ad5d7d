#!/bin/bash

echo "🔧 Fixing Jenkins Docker permissions..."

# 1. Thêm jenkins user vào docker group
echo "Adding jenkins user to docker group..."
sudo usermod -aG docker jenkins

# 2. <PERSON><PERSON><PERSON> tra docker group
echo "Checking docker group membership..."
getent group docker

# 3. Set proper permissions for docker socket
echo "Setting docker socket permissions..."
sudo chmod 666 /var/run/docker.sock

# 4. Restart services
echo "Restarting Jenkins service..."
sudo systemctl restart jenkins

echo "Waiting for <PERSON> to restart..."
sleep 10

# 5. Test docker access
echo "Testing docker access for jenkins user..."
sudo -u jenkins docker --version

if sudo -u jenkins docker ps > /dev/null 2>&1; then
    echo "✅ SUCCESS: <PERSON> can now access Docker!"
else
    echo "❌ FAILED: <PERSON> still cannot access Docker"
    echo "Manual steps required:"
    echo "1. sudo usermod -aG docker jenkins"
    echo "2. sudo systemctl restart jenkins"
    echo "3. <PERSON><PERSON><PERSON> and login jenkins user"
fi

echo "🎉 Fix completed!"
