from uuid import uuid4
from typing import Dict, Any
from datetime import timed<PERSON>ta, date, datetime

from django.conf import settings
from django.utils import timezone
from django.contrib.auth import authenticate
from django.contrib.auth.hashers import make_password

from rest_framework import serializers
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import Refresh<PERSON><PERSON>
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer, TokenRefreshSerializer

from rest_framework.fields import (  # NOQA # isort:skip
    <PERSON><PERSON>anField, Char<PERSON><PERSON>, ChoiceField, DateField, DateTimeField, DecimalField,
    DictField, DurationField, EmailField, Field, FileField, FilePathField, FloatField,
    HiddenField, HStoreField, IPAddressField, ImageField, IntegerField, JSONField,
    ListField, ModelField, MultipleChoiceField, ReadOnlyField,
    RegexField, SerializerMethod<PERSON>ield, <PERSON><PERSON>Field, TimeField, U<PERSON>Field, UUIDField,
)

from dh_index.apps.utils.constant_status import AppStatus
