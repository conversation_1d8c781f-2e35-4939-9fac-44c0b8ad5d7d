# Simple Dockerfile for debugging
FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY pyproject.toml poetry.lock* ./

# Install Poetry and dependencies
RUN pip install poetry && poetry config virtualenvs.create false && poetry install --only=main

# Copy source code
COPY . .

# Copy deployment scripts
COPY deploy/create_superuser.py .

# Expose port
EXPOSE 8000

# Simple command for testing
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
