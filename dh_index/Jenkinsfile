pipeline {
    agent any
    
    environment {
        // Docker image names
        DOCKER_IMAGE = "dh-index-backend"
        DOCKER_TAG = "${BUILD_NUMBER}"
        DOCKER_LATEST = "latest"
        
        // Docker registry (nếu sử dụng private registry)
        // DOCKER_REGISTRY = "your-registry.com"
        
        // Deployment directory
        DEPLOY_DIR = "/opt/dh-index-backend"
    }
    
    stages {
        stage('Checkout') {
            steps {
                echo 'Checking out code from GitHub...'
                checkout scm
            }
        }
        
        stage('Build Docker Image') {
            steps {
                echo 'Building Docker image...'
                script {
                    // Kiểm tra cấu trúc thư mục và build Docker image
                    sh """
                        echo "Current directory: \$(pwd)"
                        echo "Listing files:"
                        ls -la

                        # Kiểm tra xem có thư mục dh_index không
                        if [ -d "dh_index" ]; then
                            echo "Found dh_index directory"
                            cd dh_index
                        fi

                        echo "Current directory after cd: \$(pwd)"
                        echo "Listing files in current directory:"
                        ls -la

                        # Kiể<PERSON> tra thư mục deploy
                        if [ -d "deploy" ]; then
                            echo "Found deploy directory"
                            cd deploy
                            echo "Building Docker image..."
                            docker build -f Dockerfile -t ${DOCKER_IMAGE}:${DOCKER_TAG} ..
                            docker tag ${DOCKER_IMAGE}:${DOCKER_TAG} ${DOCKER_IMAGE}:${DOCKER_LATEST}
                        else
                            echo "Deploy directory not found!"
                            exit 1
                        fi
                    """
                }
            }
        }
        
        stage('Test') {
            steps {
                echo 'Running tests...'
                script {
                    // Chạy container tạm để test
                    sh """
                        docker run --rm -d --name test-backend-${BUILD_NUMBER} \
                            -p 8001:8000 ${DOCKER_IMAGE}:${DOCKER_TAG}

                        # Đợi container khởi động
                        sleep 15

                        # Test health check
                        curl -f http://localhost:8001/admin/ || exit 1

                        # Dọn dẹp
                        docker stop test-backend-${BUILD_NUMBER}
                    """
                }
            }
        }
        
        stage('Deploy') {
            when {
                branch 'main' // Chỉ deploy khi push vào main branch
            }
            steps {
                echo 'Deploying to production...'
                script {
                    sh """
                        # Tạo thư mục deploy nếu chưa có
                        sudo mkdir -p ${DEPLOY_DIR}

                        # Tìm và copy docker-compose file
                        if [ -d "dh_index/deploy" ]; then
                            sudo cp dh_index/deploy/docker-compose.prod.yml ${DEPLOY_DIR}/docker-compose.yml
                        elif [ -d "deploy" ]; then
                            sudo cp deploy/docker-compose.prod.yml ${DEPLOY_DIR}/docker-compose.yml
                        else
                            echo "Deploy directory not found!"
                            exit 1
                        fi

                        # Stop old container
                        cd ${DEPLOY_DIR}
                        sudo docker-compose down || true

                        # Remove old image
                        sudo docker rmi ${DOCKER_IMAGE}:${DOCKER_LATEST} || true

                        # Start new container
                        sudo docker-compose up -d

                        # Health check
                        sleep 20
                        curl -f http://localhost:8000/admin/ || exit 1
                    """
                }
            }
        }
        
        stage('Cleanup') {
            steps {
                echo 'Cleaning up old Docker images...'
                script {
                    sh """
                        # Xóa images cũ (giữ lại 3 bản gần nhất)
                        docker images ${DOCKER_IMAGE} --format "table {{.Tag}}" | grep -v TAG | grep -v latest | sort -nr | tail -n +4 | xargs -r docker rmi ${DOCKER_IMAGE}: || true
                    """
                }
            }
        }
    }
    
    post {
        always {
            echo 'Pipeline completed!'
            // Dọn dẹp workspace nếu cần
            cleanWs()
        }
        success {
            echo 'Deployment successful!'
            // Có thể gửi notification thành công
        }
        failure {
            echo 'Pipeline failed!'
            // Có thể gửi notification lỗi
        }
    }
}
