# Sử dụng Python 3.10 official image
FROM python:3.10-slim

# Thiết lập biến môi trường
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Thiết lập thư mục làm việc
WORKDIR /app

# Cập nhật hệ thống và cài đặt các dependencies cần thiết
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y \
        build-essential \
        curl \
        git \
        && apt-get clean \
        && rm -rf /var/lib/apt/lists/*

# Cài đặt Poetry
RUN pip install --upgrade pip && \
    pip install poetry

# Cấu hình Poetry
RUN poetry config virtualenvs.create false

# Copy poetry files
COPY pyproject.toml poetry.lock* ./

# Cài đặt dependencies
RUN poetry install --no-dev

# Copy source code
COPY . .

# <PERSON><PERSON><PERSON> thư mục logs nếu chưa có
RUN mkdir -p logs

# Collect static files (nếu có)
RUN python manage.py collectstatic --noinput || true

# Chạy migrations
RUN python manage.py migrate || true

# Expose port 8000
EXPOSE 8000

# Command để chạy server
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
