Started by user dh admin
Obtained <PERSON><PERSON><PERSON> <NAME_EMAIL>:daihiep-index/dh-index-be.git
[Pipeline] Start of Pipeline
[Pipeline] node
Running on Jenkins in /var/lib/jenkins/workspace/first-index/first-job
[Pipeline] {
[Pipeline] stage
[Pipeline] { (Declarative: Checkout SCM)
[Pipeline] checkout
Selected Git installation does not exist. Using Default
The recommended git tool is: NONE
using credential github-private-key
 > git rev-parse --resolve-git-dir /var/lib/jenkins/workspace/first-index/first-job/.git # timeout=10
Fetching changes from the remote Git repository
 > git config remote.origin.url **************:daihiep-index/dh-index-be.git # timeout=10
Fetching upstream <NAME_EMAIL>:daihiep-index/dh-index-be.git
 > git --version # timeout=10
 > git --version # 'git version 2.43.0'
using GIT_SSH to set credentials 
Verifying host key using known hosts file
 > git fetch --tags --force --progress -- **************:daihiep-index/dh-index-be.git +refs/heads/*:refs/remotes/origin/* # timeout=10
 > git rev-parse refs/remotes/origin/main^{commit} # timeout=10
Checking out Revision cdb33e286bf80111958c59cb1ef452c0cdcf6905 (refs/remotes/origin/main)
 > git config core.sparsecheckout # timeout=10
 > git checkout -f cdb33e286bf80111958c59cb1ef452c0cdcf6905 # timeout=10
Commit message: "feat: jenkinsfile"
First time build. Skipping changelog.
[Pipeline] }
[Pipeline] // stage
[Pipeline] withEnv
[Pipeline] {
[Pipeline] withEnv
[Pipeline] {
[Pipeline] stage
[Pipeline] { (Checkout)
[Pipeline] echo
Checking out code from GitHub...
[Pipeline] checkout
Selected Git installation does not exist. Using Default
The recommended git tool is: NONE
using credential github-private-key
 > git rev-parse --resolve-git-dir /var/lib/jenkins/workspace/first-index/first-job/.git # timeout=10
Fetching changes from the remote Git repository
 > git config remote.origin.url **************:daihiep-index/dh-index-be.git # timeout=10
Fetching upstream <NAME_EMAIL>:daihiep-index/dh-index-be.git
 > git --version # timeout=10
 > git --version # 'git version 2.43.0'
using GIT_SSH to set credentials 
Verifying host key using known hosts file
 > git fetch --tags --force --progress -- **************:daihiep-index/dh-index-be.git +refs/heads/*:refs/remotes/origin/* # timeout=10
 > git rev-parse refs/remotes/origin/main^{commit} # timeout=10
Checking out Revision cdb33e286bf80111958c59cb1ef452c0cdcf6905 (refs/remotes/origin/main)
 > git config core.sparsecheckout # timeout=10
 > git checkout -f cdb33e286bf80111958c59cb1ef452c0cdcf6905 # timeout=10
Commit message: "feat: jenkinsfile"
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Build Docker Image)
[Pipeline] echo
Building Docker image...
[Pipeline] dir
Running in /var/lib/jenkins/workspace/first-index/first-job/dh_index
[Pipeline] {
[Pipeline] script
[Pipeline] {
[Pipeline] sh
+ cd deploy
/var/lib/jenkins/workspace/first-index/first-job/dh_index@tmp/durable-641e3ec1/script.sh.copy: 2: cd: can't cd to deploy
[Pipeline] }
[Pipeline] // script
[Pipeline] }
[Pipeline] // dir
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Test)
Stage "Test" skipped due to earlier failure(s)
[Pipeline] getContext
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Deploy)
Stage "Deploy" skipped due to earlier failure(s)
[Pipeline] getContext
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Cleanup)
Stage "Cleanup" skipped due to earlier failure(s)
[Pipeline] getContext
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Declarative: Post Actions)
[Pipeline] echo
Pipeline completed!
[Pipeline] cleanWs
[WS-CLEANUP] Deleting project workspace...
[WS-CLEANUP] Deferred wipeout is used...
[WS-CLEANUP] done
[Pipeline] echo
Pipeline failed!
[Pipeline] }
[Pipeline] // stage
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] }
[Pipeline] // node
[Pipeline] End of Pipeline
ERROR: script returned exit code 2
Finished: FAILURE
